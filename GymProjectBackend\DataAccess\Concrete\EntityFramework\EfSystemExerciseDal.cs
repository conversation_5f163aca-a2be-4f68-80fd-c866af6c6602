using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfSystemExerciseDal : EfEntityRepositoryBase<SystemExercise, GymContext>, ISystemExerciseDal
    {
        // Constructor injection (Scalability için)
        public EfSystemExerciseDal(GymContext context) : base(context)
        {
        }

        public List<SystemExerciseDto> GetAllSystemExercises()
        {
            var result = from se in _context.SystemExercises
                         join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where se.IsActive == true && ec.IsActive == true
                         orderby ec.CategoryName, se.ExerciseName
                         select new SystemExerciseDto
                         {
                             SystemExerciseID = se.SystemExerciseID,
                             ExerciseCategoryID = se.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             ExerciseName = se.ExerciseName,
                             Description = se.Description,
                             Instructions = se.Instructions,
                             MuscleGroups = se.MuscleGroups,
                             Equipment = se.Equipment,
                             DifficultyLevel = se.DifficultyLevel,
                             DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                  se.DifficultyLevel == 2 ? "Orta" :
                                                  se.DifficultyLevel == 3 ? "İleri" : "",
                             IsActive = se.IsActive,
                             CreationDate = se.CreationDate
                         };
            return result.ToList();
        }

        public List<SystemExerciseDto> GetSystemExercisesByCategory(int categoryId)
        {
            var result = from se in _context.SystemExercises
                         join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where se.ExerciseCategoryID == categoryId && se.IsActive == true && ec.IsActive == true
                         orderby se.ExerciseName
                         select new SystemExerciseDto
                         {
                             SystemExerciseID = se.SystemExerciseID,
                             ExerciseCategoryID = se.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             ExerciseName = se.ExerciseName,
                             Description = se.Description,
                             Instructions = se.Instructions,
                             MuscleGroups = se.MuscleGroups,
                             Equipment = se.Equipment,
                             DifficultyLevel = se.DifficultyLevel,
                             DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                  se.DifficultyLevel == 2 ? "Orta" :
                                                  se.DifficultyLevel == 3 ? "İleri" : "",
                             IsActive = se.IsActive,
                             CreationDate = se.CreationDate
                         };
            return result.ToList();
        }

        public PaginatedResult<SystemExerciseDto> GetSystemExercisesFiltered(SystemExerciseFilterDto filter)
        {
            var query = from se in _context.SystemExercises
                       join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                       where se.IsActive == true && ec.IsActive == true
                       select new SystemExerciseDto
                       {
                           SystemExerciseID = se.SystemExerciseID,
                           ExerciseCategoryID = se.ExerciseCategoryID,
                           CategoryName = ec.CategoryName,
                           ExerciseName = se.ExerciseName,
                           Description = se.Description,
                           Instructions = se.Instructions,
                           MuscleGroups = se.MuscleGroups,
                           Equipment = se.Equipment,
                           DifficultyLevel = se.DifficultyLevel,
                           DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                se.DifficultyLevel == 2 ? "Orta" :
                                                se.DifficultyLevel == 3 ? "İleri" : "",
                           IsActive = se.IsActive,
                           CreationDate = se.CreationDate
                       };

                // Filtreleme
                if (filter.ExerciseCategoryID.HasValue)
                {
                    query = query.Where(x => x.ExerciseCategoryID == filter.ExerciseCategoryID.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                {
                    var searchTerm = filter.SearchTerm.ToLower();
                    query = query.Where(x =>
                        (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                        (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                        (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
                }

                if (filter.DifficultyLevel.HasValue)
                {
                    query = query.Where(x => x.DifficultyLevel == filter.DifficultyLevel.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.Equipment))
                {
                    var equipment = filter.Equipment.ToLower();
                    query = query.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
                }

                // Sıralama
                query = query.OrderBy(x => x.CategoryName).ThenBy(x => x.ExerciseName);

            return query.ToPaginatedResult(filter.Page, filter.PageSize);
        }

        public List<SystemExerciseDto> SearchSystemExercises(string searchTerm)
        {
            // Null kontrolü ekle
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<SystemExerciseDto>();
            }

            var result = from se in _context.SystemExercises
                         join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where se.IsActive == true && ec.IsActive == true &&
                               (se.ExerciseName.Contains(searchTerm) ||
                                (se.Description != null && se.Description.Contains(searchTerm)) ||
                                (se.MuscleGroups != null && se.MuscleGroups.Contains(searchTerm)) ||
                                ec.CategoryName.Contains(searchTerm))
                             orderby se.ExerciseName
                             select new SystemExerciseDto
                             {
                                 SystemExerciseID = se.SystemExerciseID,
                                 ExerciseCategoryID = se.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = se.ExerciseName,
                                 Description = se.Description,
                                 Instructions = se.Instructions,
                                 MuscleGroups = se.MuscleGroups,
                                 Equipment = se.Equipment,
                                 DifficultyLevel = se.DifficultyLevel,
                                 DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                      se.DifficultyLevel == 2 ? "Orta" :
                                                      se.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = se.IsActive,
                                 CreationDate = se.CreationDate
                             };
                return result.ToList();
        }

        public SystemExerciseDto GetSystemExerciseDetail(int exerciseId)
        {
            var result = from se in _context.SystemExercises
                         join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where se.SystemExerciseID == exerciseId && se.IsActive == true
                         select new SystemExerciseDto
                         {
                             SystemExerciseID = se.SystemExerciseID,
                             ExerciseCategoryID = se.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             ExerciseName = se.ExerciseName,
                             Description = se.Description,
                             Instructions = se.Instructions,
                             MuscleGroups = se.MuscleGroups,
                             Equipment = se.Equipment,
                             DifficultyLevel = se.DifficultyLevel,
                             DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                  se.DifficultyLevel == 2 ? "Orta" :
                                                  se.DifficultyLevel == 3 ? "İleri" : "",
                             IsActive = se.IsActive,
                             CreationDate = se.CreationDate
                         };
            return result.FirstOrDefault();
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve soft delete logic DAL katmanında
        /// </summary>
        public IResult SoftDeleteSystemExerciseWithValidation(int exerciseId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var exercise = _context.SystemExercises.FirstOrDefault(e => e.SystemExerciseID == exerciseId);
                    if (exercise == null)
                    {
                        return new ErrorResult("Egzersiz bulunamadı");
                    }

                    // Soft delete
                    exercise.IsActive = false;
                    exercise.DeletedDate = DateTime.Now;
                    exercise.UpdatedDate = DateTime.Now;

                    _context.SaveChanges();
                    return new SuccessResult("Egzersiz başarıyla silindi");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var exercise = context.SystemExercises.FirstOrDefault(e => e.SystemExerciseID == exerciseId);
                        if (exercise == null)
                        {
                            return new ErrorResult("Egzersiz bulunamadı");
                        }

                        // Soft delete
                        exercise.IsActive = false;
                        exercise.DeletedDate = DateTime.Now;
                        exercise.UpdatedDate = DateTime.Now;

                        context.SystemExercises.Update(exercise);
                        context.SaveChanges();
                        return new SuccessResult("Egzersiz başarıyla silindi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz silinirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Entity oluşturma ve tarih yönetimi DAL katmanında
        /// </summary>
        public IResult AddSystemExerciseWithManagement(SystemExerciseAddDto exerciseAddDto)
        {
            try
            {
                var exercise = new SystemExercise
                {
                    ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                    ExerciseName = exerciseAddDto.ExerciseName,
                    Description = exerciseAddDto.Description,
                    Instructions = exerciseAddDto.Instructions,
                    MuscleGroups = exerciseAddDto.MuscleGroups,
                    Equipment = exerciseAddDto.Equipment,
                    DifficultyLevel = exerciseAddDto.DifficultyLevel
                    // IsActive ve CreationDate EfEntityRepositoryBase tarafından otomatik set edilecek
                };

                Add(exercise);
                return new SuccessResult("Sistem egzersizi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Sistem egzersizi eklenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Entity güncelleme ve tarih yönetimi DAL katmanında
        /// </summary>
        public IResult UpdateSystemExerciseWithManagement(SystemExerciseUpdateDto exerciseUpdateDto)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var existingExercise = _context.SystemExercises.FirstOrDefault(e => e.SystemExerciseID == exerciseUpdateDto.SystemExerciseID);
                    if (existingExercise == null)
                    {
                        return new ErrorResult("Sistem egzersizi bulunamadı.");
                    }

                    existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                    existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                    existingExercise.Description = exerciseUpdateDto.Description;
                    existingExercise.Instructions = exerciseUpdateDto.Instructions;
                    existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                    existingExercise.Equipment = exerciseUpdateDto.Equipment;
                    existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                    existingExercise.IsActive = exerciseUpdateDto.IsActive;
                    // UpdatedDate EfEntityRepositoryBase tarafından otomatik set edilecek

                    Update(existingExercise);
                    return new SuccessResult("Sistem egzersizi başarıyla güncellendi.");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var existingExercise = context.SystemExercises.FirstOrDefault(e => e.SystemExerciseID == exerciseUpdateDto.SystemExerciseID);
                        if (existingExercise == null)
                        {
                            return new ErrorResult("Sistem egzersizi bulunamadı.");
                        }

                        existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                        existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                        existingExercise.Description = exerciseUpdateDto.Description;
                        existingExercise.Instructions = exerciseUpdateDto.Instructions;
                        existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                        existingExercise.Equipment = exerciseUpdateDto.Equipment;
                        existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                        existingExercise.IsActive = exerciseUpdateDto.IsActive;
                        existingExercise.UpdatedDate = DateTime.Now;

                        context.SystemExercises.Update(existingExercise);
                        context.SaveChanges();
                        return new SuccessResult("Sistem egzersizi başarıyla güncellendi.");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Sistem egzersizi güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
