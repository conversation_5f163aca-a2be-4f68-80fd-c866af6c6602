﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        // Constructor injection (Scalability için)
        public EfUserDal(GymContext context) : base(context)
        {
        }

        public List<OperationClaim> GetClaims(User user)
        {
            var result = from OperationClaim in _context.OperationClaims
                         join UserOperationClaim in _context.UserOperationClaims
                         on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                         where UserOperationClaim.UserId == user.UserID
                         select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return result.ToList();
        }

        /// <summary>
        /// Member rolü olmayan tüm kullanıcıları getirir (Küçük sistemler için)
        /// </summary>
        public List<User> GetNonMembers()
        {
            // DI kullanılıyor - Scalability optimized
            // Member rolünün ID'sini al (Cache'lenebilir)
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                if (memberRoleId == 0)
                {
                    // Member rolü yoksa tüm aktif kullanıcıları döner
                    return _context.Users
                        .Where(u => u.IsActive)
                        .OrderBy(u => u.FirstName)
                        .ThenBy(u => u.LastName)
                        .ToList();
                }

                // Member rolüne sahip kullanıcıların ID'lerini al
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId)
                    .ToHashSet(); // HashSet performans için

                // Member rolü olmayan kullanıcıları getir
                return _context.Users
                    .Where(u => u.IsActive && !usersWithMemberRole.Contains(u.UserID))
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToList();
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları sayfalı olarak getirir (10K+ kullanıcı için optimize)
        /// </summary>
        public List<User> GetNonMembersPaginated(int page, int pageSize, string searchTerm)
        {
            // DI kullanılıyor - Scalability optimized
            // Member rolünün ID'sini al
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                var query = _context.Users.AsQueryable();

                // Aktif kullanıcılar
                query = query.Where(u => u.IsActive);

                // Member rolü varsa, member olmayan kullanıcıları filtrele
                if (memberRoleId > 0)
                {
                    var usersWithMemberRole = _context.UserOperationClaims
                        .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                        .Select(uoc => uoc.UserId);

                    query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
                }

                // Arama terimi varsa filtrele
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(u =>
                        u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                        u.LastName.ToLower().Contains(lowerSearchTerm) ||
                        u.Email.ToLower().Contains(lowerSearchTerm));
                }

                // Sayfalama ve sıralama
                return query
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
        }

        /// <summary>
        /// Member rolü olmayan kullanıcı sayısını getirir
        /// </summary>
        public int GetNonMembersCount(string searchTerm)
        {
            // DI kullanılıyor - Scalability optimized
            // Member rolünün ID'sini al
            var memberRoleId = _context.OperationClaims
                    .Where(oc => oc.Name == "member" && oc.IsActive == true)
                    .Select(oc => oc.OperationClaimId)
                    .FirstOrDefault();

                var query = _context.Users.AsQueryable();

                // Aktif kullanıcılar
                query = query.Where(u => u.IsActive);

                // Member rolü varsa, member olmayan kullanıcıları filtrele
                if (memberRoleId > 0)
                {
                    var usersWithMemberRole = _context.UserOperationClaims
                        .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                        .Select(uoc => uoc.UserId);

                    query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
                }

                // Arama terimi varsa filtrele
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(u =>
                        u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                        u.LastName.ToLower().Contains(lowerSearchTerm) ||
                        u.Email.ToLower().Contains(lowerSearchTerm));
                }

                return query.Count();
        }

        /// <summary>
        /// User tablosu güncellendiğinde CompanyUser tablosundaki ilgili kayıtları senkronize eder
        /// SOLID prensiplerine uygun: Complex database operations DAL katmanında
        /// </summary>
        public IResult SyncCompanyUserData(User updatedUser, User oldUser)
        {
            try
            {
                using (var context = new GymContext())
                {
                    // Bu User'a ait CompanyUser kayıtlarını bul
                    // Email ile eşleşen CompanyUser'ları bul (çünkü User.Email = CompanyUser.Email)
                    var companyUsers = context.CompanyUsers
                        .Where(cu => cu.Email == oldUser.Email && cu.IsActive == true)
                        .ToList();

                    // CompanyUser kayıtlarını güncelle - SQL ile direkt güncelleme
                    string newEmail = updatedUser.Email;
                    string newFullName = $"{updatedUser.FirstName} {updatedUser.LastName}".Trim();

                    foreach (var companyUser in companyUsers)
                    {
                        bool emailChanged = companyUser.Email != newEmail;
                        bool nameChanged = companyUser.Name != newFullName;

                        if (emailChanged || nameChanged)
                        {
                            // SQL ile direkt güncelleme yap - Entity Framework context'ini hiç kullanma
                            string updateSql = @"
                                UPDATE CompanyUsers
                                SET Email = @Email,
                                    Name = @Name,
                                    UpdatedDate = @UpdatedDate
                                WHERE CompanyUserID = @CompanyUserID";

                            context.Database.ExecuteSqlRaw(updateSql,
                                new Microsoft.Data.SqlClient.SqlParameter("@Email", newEmail),
                                new Microsoft.Data.SqlClient.SqlParameter("@Name", newFullName),
                                new Microsoft.Data.SqlClient.SqlParameter("@UpdatedDate", DateTime.Now),
                                new Microsoft.Data.SqlClient.SqlParameter("@CompanyUserID", companyUser.CompanyUserID));
                        }
                    }

                    return new SuccessResult("CompanyUser verileri başarıyla senkronize edildi.");
                }
            }
            catch (Exception ex)
            {
                // Hata durumunda detaylı log ve error result döner
                return new ErrorResult($"CompanyUser senkronizasyonu sırasında hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path güncelleme işlemi DAL katmanında
        /// </summary>
        public IResult UpdateProfileImagePath(int userId, string imagePath)
        {
            try
            {
                using (var context = new GymContext())
                {
                    var user = context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                    if (user == null)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı.");
                    }

                    user.ProfileImagePath = imagePath;
                    user.UpdatedDate = DateTime.Now;
                    context.Users.Update(user);
                    context.SaveChanges();

                    return new SuccessResult("Profil fotoğrafı yolu başarıyla güncellendi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil fotoğrafı yolu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path temizleme işlemi DAL katmanında
        /// </summary>
        public IResult ClearProfileImagePath(int userId)
        {
            try
            {
                using (var context = new GymContext())
                {
                    var user = context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                    if (user == null)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı.");
                    }

                    user.ProfileImagePath = null;
                    user.UpdatedDate = DateTime.Now;
                    context.Users.Update(user);
                    context.SaveChanges();

                    return new SuccessResult("Profil fotoğrafı yolu başarıyla temizlendi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil fotoğrafı yolu temizlenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katmanında
        /// </summary>
        public IDataResult<User> GetUserByIdWithValidation(int userId)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                var user = _context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorDataResult<User>("Kullanıcı bulunamadı");
                }
                return new SuccessDataResult<User>(user);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<User>($"Kullanıcı getirilirken hata oluştu: {ex.Message}");
            }
        }

    }
}
